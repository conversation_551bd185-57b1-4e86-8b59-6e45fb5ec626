<{capture name="header"}>
<{css app="ome" src="ome.css"}>
<{css app="ome" src="style.css"}>
<{script src="coms/modedialog.js" app="desktop"}>
<{script src="coms/pager.js" app="desktop"}>
<{/capture}>

<style>
    /* 重置和基础样式 - 兼容框架 */
    .repair-container * {
        box-sizing: border-box;
    }

    /* 主容器样式 - 适配框架布局 */
    .repair-container {
        max-width: 100%;
        margin: 0;
        padding: 1rem;
        background: #f8f9fa;
        min-height: calc(100vh - 100px);
        font-family: Arial, Helvetica, sans-serif, "宋体";
        font-size: 12px;
    }

    /* 标题样式 - 兼容框架头部 */
    .repair-header {
        text-align: center;
        margin-bottom: 2rem;
        color: #4f638f;
        font-size: 18px;
        font-weight: bold;
        padding: 1rem 0;
        background: #fff;
        border: 1px solid #d5dfe3;
        border-radius: 4px;
        position: relative;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .repair-header::after {
        content: '';
        display: block;
        width: 60px;
        height: 3px;
        background: linear-gradient(90deg, #3367AC, #4f638f);
        margin: 0.5rem auto 0;
        border-radius: 2px;
    }

    /* 功能列表样式 - 兼容框架网格布局 */
    .function-list {
        display: block;
        margin-bottom: 1.5rem;
        overflow: hidden;
    }

    .function-item {
        background: #fff;
        border: 1px solid #d5dfe3;
        border-radius: 4px;
        padding: 12px 15px;
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: left;
        position: relative;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        margin-bottom: 8px;
        float: left;
        width: 48%;
        margin-right: 2%;
        min-height: 80px;
    }

    .function-item:nth-child(2n) {
        margin-right: 0;
    }

    .function-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: #3367AC;
        transform: scaleX(0);
        transition: transform 0.2s ease;
    }

    .function-item:hover {
        border-color: #3367AC;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(51, 103, 172, 0.15);
        background: #f8faff;
    }

    .function-item:hover::before {
        transform: scaleX(1);
    }

    .function-item.active {
        background: #3367AC;
        color: white;
        border-color: #3367AC;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(51, 103, 172, 0.3);
    }

    .function-item.active::before {
        background: rgba(255, 255, 255, 0.3);
        transform: scaleX(1);
    }

    .function-item h3 {
        margin: 0 0 6px 0;
        color: #4f638f;
        font-size: 14px;
        font-weight: bold;
        line-height: 1.4;
        display: block;
    }

    .function-item.active h3 {
        color: white;
    }

    .function-item p {
        margin: 0;
        color: #666;
        font-size: 12px;
        line-height: 1.4;
    }

    .function-item.active p {
        color: rgba(255, 255, 255, 0.9);
    }

    .function-item::after {
        content: '→';
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #3367AC;
        font-size: 14px;
        opacity: 0;
        transition: all 0.2s ease;
    }

    .function-item:hover::after,
    .function-item.active::after {
        opacity: 1;
        transform: translateY(-50%) translateX(2px);
    }

    .function-item.active::after {
        color: white;
    }

    /* 操作区域样式 - 兼容框架布局 */
    .operation-area {
        display: none;
        margin-top: 1.5rem;
        clear: both;
    }

    .operation-area.active {
        display: block;
    }

    @media (min-width: 768px) {
        .operation-area.active {
            display: table;
            width: 100%;
            table-layout: fixed;
        }

        .repair-form-container,
        .response-container {
            display: table-cell;
            vertical-align: top;
        }

        .repair-form-container {
            width: 60%;
            padding-right: 15px;
        }

        .response-container {
            width: 40%;
            padding-left: 15px;
        }
    }

    .repair-form-container {
        background: #fff;
        border: 1px solid #d5dfe3;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        padding: 15px;
        margin-bottom: 15px;
    }

    .repair-form-container h3 {
        color: #4f638f;
        margin: 0 0 12px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #e5e7eb;
        font-size: 14px;
        font-weight: bold;
        display: block;
    }

    .repair-form {
        display: block;
        background: #f9fafb;
        padding: 12px;
        border-radius: 4px;
        margin-top: 12px;
        border: 1px solid #e5e7eb;
    }

    .repair-form p {
        margin: 0 0 6px 0;
        color: #333;
        font-size: 12px;
        line-height: 1.4;
        font-weight: normal;
    }

    .repair-form input[type="text"],
    .repair-form textarea,
    .repair-form select {
        padding: 6px 8px;
        border: 1px solid #d1d5db;
        border-radius: 3px;
        font-size: 12px;
        transition: all 0.2s ease;
        width: 100%;
        font-family: Arial, Helvetica, sans-serif, "宋体";
        background: #ffffff;
        line-height: 1.4;
        margin-bottom: 8px;
    }

    .repair-form input[type="text"]:focus,
    .repair-form textarea:focus,
    .repair-form select:focus {
        border-color: #3367AC;
        outline: none;
        box-shadow: 0 0 0 2px rgba(51, 103, 172, 0.1);
        background: #ffffff;
    }

    .repair-form textarea {
        resize: vertical;
        min-height: 60px;
        font-family: Arial, Helvetica, sans-serif, "宋体";
    }

    .repair-form select {
        cursor: pointer;
        background: #ffffff;
        padding-right: 20px;
    }

    .repair-submit {
        background: #3367AC;
        color: white;
        padding: 8px 16px;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        font-size: 12px;
        font-weight: normal;
        transition: all 0.2s ease;
        font-family: Arial, Helvetica, sans-serif, "宋体";
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        position: relative;
        margin-top: 8px;
        min-width: 80px;
        line-height: 1.4;
    }

    .repair-submit:hover {
        background: #2856a0;
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(51, 103, 172, 0.2);
    }

    .repair-submit:active {
        transform: translateY(0);
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .repair-submit:disabled {
        background: #9ca3af;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
        opacity: 0.6;
    }

    .response-container {
        background: #fff;
        border: 1px solid #d5dfe3;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        padding: 15px;
        margin-bottom: 15px;
    }

    .response-container h3 {
        color: #4f638f;
        margin: 0 0 12px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #e5e7eb;
        font-size: 14px;
        font-weight: bold;
        display: block;
    }

    .response-html {
        padding: 12px;
        background: #f9fafb;
        border-radius: 4px;
        min-height: 150px;
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #e5e7eb;
        font-size: 12px;
        line-height: 1.5;
        font-family: Arial, Helvetica, sans-serif, "宋体";
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }

    .loading-content {
        background: #fff;
        padding: 20px;
        border-radius: 4px;
        text-align: center;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        border: 1px solid #d5dfe3;
    }

    .loading-spinner {
        width: 30px;
        height: 30px;
        border: 3px solid #e5e7eb;
        border-top: 3px solid #3367AC;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 12px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .loading-text {
        color: #333;
        font-size: 12px;
        font-weight: normal;
        margin: 0;
        font-family: Arial, Helvetica, sans-serif, "宋体";
    }

    .success-message {
        color: #166534;
        padding: 8px 12px;
        background: #f0fdf4;
        border-radius: 4px;
        margin: 8px 0;
        border-left: 3px solid #22c55e;
        font-size: 12px;
        font-family: Arial, Helvetica, sans-serif, "宋体";
    }

    .error-message {
        color: #991b1b;
        padding: 8px 12px;
        background: #fef2f2;
        border-radius: 4px;
        margin: 8px 0;
        border-left: 3px solid #ef4444;
        font-size: 12px;
        font-family: Arial, Helvetica, sans-serif, "宋体";
    }

    .back-button {
        background: #6b7280;
        color: white;
        padding: 8px 16px;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        font-size: 12px;
        font-weight: normal;
        margin-bottom: 12px;
        transition: all 0.2s ease;
        font-family: Arial, Helvetica, sans-serif, "宋体";
    }

    .back-button:hover {
        background: #4b5563;
        transform: translateY(-1px);
    }

    /* 移动端优化 */
    @media (max-width: 767px) {
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .repair-container {
            padding: 1rem 0.75rem;
        }

        .repair-header {
            font-size: 2rem;
            margin-bottom: 2rem;
        }

        .function-list {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .function-item {
            padding: 1.25rem;
        }

        .function-item h3 {
            font-size: 1rem;
        }

        .operation-area {
            gap: 1.5rem;
            margin-top: 1.5rem;
        }

        .repair-form-container,
        .response-container {
            padding: 1.25rem;
        }

        .response-container {
            position: static;
        }

        .repair-form {
            padding: 1.25rem;
        }

        .repair-submit {
            width: 100%;
            align-self: stretch;
            padding: 1rem 1.5rem;
        }

        .back-button {
            width: 100%;
            justify-content: center;
        }
    }

    /* 超小屏幕优化 */
    @media (max-width: 480px) {
        .repair-container {
            padding: 1rem 0.5rem;
        }

        .repair-header {
            font-size: 1.75rem;
            margin-bottom: 1.5rem;
        }

        .function-item {
            padding: 1rem;
        }

        .repair-form-container,
        .response-container {
            padding: 1rem;
        }

        .repair-form {
            padding: 1rem;
        }
    }

    /* 深色模式支持 */
    @media (prefers-color-scheme: dark) {
        .function-item {
            background: rgba(30, 41, 59, 0.95);
            color: #e2e8f0;
        }

        .function-item h3 {
            color: #f1f5f9;
        }

        .function-item p {
            color: #94a3b8;
        }

        .repair-form-container,
        .response-container {
            background: rgba(30, 41, 59, 0.95);
            color: #e2e8f0;
        }

        .repair-form-container h3,
        .response-container h3 {
            color: #f1f5f9;
        }

        .repair-form {
            background: rgba(15, 23, 42, 0.5);
        }

        .response-html {
            background: rgba(15, 23, 42, 0.5);
            color: #e2e8f0;
        }

        .loading-content {
            background: rgba(30, 41, 59, 0.95);
            color: #e2e8f0;
        }
    }

    /* 动画增强 */
    .function-item,
    .repair-form-container,
    .response-container {
        animation: slideInUp 0.6s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 滚动条美化 */
    .response-html::-webkit-scrollbar {
        width: 8px;
    }

    .response-html::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 4px;
    }

    .response-html::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 4px;
    }

    .response-html::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #5a67d8, #6b46c1);
    }
</style>

<div class="repair-container">
    <h1 class="repair-header">IT运维工具</h1>

    <!-- 功能选择列表 -->
    <div class="function-list" id="function-list">
        <{if $html}>
        <{foreach from=$html key=key item=value}>
        <div class="function-item" data-function="<{$key}>">
            <h3><{$value.desc}></h3>
            <p>点击选择此功能进行操作</p>
        </div>
        <{/foreach}>
        <{/if}>
    </div>

    <!-- 操作区域 -->
    <div class="operation-area" id="operation-area">
        <div class="repair-form-container">
            <button class="back-button" onclick="backToFunctionList()">返回功能列表</button>
            <h3 id="current-function-title">功能操作</h3>
            <div class="repair-form" id="repair-form" data-url="<{$process_url}>">
                <!-- 动态加载的表单内容 -->
            </div>
        </div>

        <div class="response-container">
            <h3>执行结果</h3>
            <div class="response-html" id="pair_right_response_html">
                <p style="color: #666; text-align: center; padding: 2rem;">请选择功能并执行操作，结果将在此处显示</p>
            </div>
        </div>
    </div>
</div>

<div class="loading-overlay" id="cover_float">
    <div class="loading-content">
        <div class="loading-spinner"></div>
        <div>处理中，请稍候...</div>
    </div>
</div>

<!-- 隐藏的表单模板 -->
<div style="display: none;">
    <{if $html}>
    <{foreach from=$html key=key item=value}>
    <div id="form-template-<{$key}>">
        <{$value.html}>
        <button class="repair-submit" data-sub-type="pair_submit_item">提交</button>
    </div>
    <{/foreach}>
    <{/if}>
</div>

<!-- 功能数据配置 -->
<script type="text/javascript">
    window.repairFunctionData = {
        <{if $html}>
        <{foreach from=$html key=key item=value name=func_loop}>
        '<{$key}>': {
            title: '<{$value.desc|escape:"javascript"}>',
            html: null
        }<{if !$smarty.foreach.func_loop.last}>,<{/if}>
        <{/foreach}>
        <{/if}>
    };
</script>

<script>
    // 全局变量
    let currentFunction = null;
    let functionData = window.repairFunctionData || {};

    // 初始化功能数据
    function initFunctionData() {
        // 获取表单HTML内容
        for (let key in functionData) {
            const templateEl = $('form-template-' + key);
            if (templateEl) {
                functionData[key].html = templateEl.get('html');
            }
        }
    }

    // 页面加载完成后初始化
    window.addEvent('domready', function() {
        initFunctionData();
        initFunctionSelection();
        initFormSubmission();
    });

    // 初始化功能选择
    function initFunctionSelection() {
        $$('.function-item').addEvent('click', function() {
            const functionKey = this.get('data-function');
            selectFunction(functionKey);
        });
    }

    // 选择功能
    function selectFunction(functionKey) {
        if (!functionData[functionKey]) return;

        currentFunction = functionKey;

        // 更新UI状态
        $$('.function-item').removeClass('active');
        $$('.function-item[data-function="' + functionKey + '"]').addClass('active');

        // 显示操作区域
        $('function-list').setStyle('display', 'none');
        $('operation-area').addClass('active');

        // 更新标题和表单内容
        $('current-function-title').set('text', functionData[functionKey].title);
        $('repair-form').set('html', functionData[functionKey].html);

        // 重新绑定提交事件
        initFormSubmission();

        // 清空结果区域
        responseEmpty();
    }

    // 返回功能列表
    function backToFunctionList() {
        $('function-list').setStyle('display', 'grid');
        $('operation-area').removeClass('active');
        $$('.function-item').removeClass('active');
        currentFunction = null;
        responseEmpty();
    }

    // 初始化表单提交
    function initFormSubmission() {
        // 移除旧的事件监听器，避免重复绑定
        $$('.repair-submit').removeEvents('click');

        $$('.repair-submit').addEvent('click', function() {
            if (!currentFunction) return;

            const button = this;
            const form = button.getParent('.repair-form');
            const subUrl = form.get('data-url');
            const subDataString = form.toQueryString();
            const taskName = functionData[currentFunction].title;

            // 禁用提交按钮
            button.set('disabled', true);
            button.set('text', '处理中...');

            responseEmpty();

            const request = new Request({
                url: subUrl,
                method: 'post',
                data: subDataString,
                onRequest: function() {
                    $("cover_float").setStyle('display', 'flex');
                },
                onSuccess: function(responseText) {
                    $("cover_float").setStyle('display', 'none');
                    responseHandle(responseText, taskName);

                    // 恢复提交按钮
                    button.set('disabled', false);
                    button.set('text', '提交');
                },
                onFailure: function() {
                    $("cover_float").setStyle('display', 'none');
                    responseHandle('{"code":500,"msg":"网络请求失败，请检查网络连接后重试"}', taskName);

                    // 恢复提交按钮
                    button.set('disabled', false);
                    button.set('text', '提交');
                }
            });

            request.send();
        });
    }

    // 处理响应结果
    function responseHandle(responseText, taskName) {
        const responseAreaHtml = $("pair_right_response_html");
        let json;

        try {
            json = JSON.decode(responseText);
        } catch (e) {
            json = {code: 500, msg: '响应数据格式错误'};
        }

        let html = '';
        const timestamp = new Date().toLocaleString();

        html += `<div style="border-bottom: 1px solid #e0e0e0; padding-bottom: 0.5rem; margin-bottom: 1rem;">`;
        html += `<h4 style="margin: 0; color: #2c3e50;">${taskName}</h4>`;
        html += `<small style="color: #666;">执行时间: ${timestamp}</small>`;
        html += `</div>`;

        if(json.code == 200) {
            html += `<div class="success-message">`;
            html += `<strong>✓ 执行成功</strong><br>`;
            html += `${json.msg}`;
            html += `</div>`;
        } else if(json.code == 500) {
            html += `<div class="error-message">`;
            html += `<strong>✗ 执行失败</strong><br>`;
            html += `${json.msg}`;
            html += `</div>`;
        } else {
            html += `<div class="error-message">`;
            html += `<strong>? 未知状态</strong><br>`;
            html += `响应代码: ${json.code}<br>`;
            html += `响应消息: ${json.msg || '无消息'}`;
            html += `</div>`;
        }

        responseAreaHtml.set('html', html);

        // 滚动到结果区域（移动端优化）
        if (window.innerWidth <= 767) {
            responseAreaHtml.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }

    // 清空响应区域
    function responseEmpty() {
        $("pair_right_response_html").set('html', '<p style="color: #666; text-align: center; padding: 2rem;">请执行操作，结果将在此处显示</p>');
    }

    // 键盘快捷键支持
    document.addEvent('keydown', function(e) {
        // ESC键返回功能列表
        if (e.key === 'Escape' && currentFunction) {
            backToFunctionList();
        }
    });

    // 移动端触摸优化
    if ('ontouchstart' in window) {
        $$('.function-item').addEvent('touchstart', function() {
            this.addClass('active');
        });

        $$('.function-item').addEvent('touchend', function() {
            // 延迟移除active状态，避免点击效果过快消失
            setTimeout(() => {
                if (this.get('data-function') !== currentFunction) {
                    this.removeClass('active');
                }
            }, 150);
        });
    }
</script>
