<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IT运维工具 - 新设计演示</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
        }

        .repair-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1rem;
            min-height: 100vh;
        }

        .repair-header {
            text-align: center;
            margin-bottom: 3rem;
            color: #ffffff;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .repair-header::after {
            content: '';
            display: block;
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
            margin: 1rem auto 0;
            border-radius: 2px;
        }

        /* 功能列表样式 */
        .function-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .function-item {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 16px;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-align: left;
            position: relative;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            animation: slideInUp 0.6s ease-out;
        }

        .function-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .function-item:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            background: rgba(255, 255, 255, 1);
        }

        .function-item:hover::before {
            transform: scaleX(1);
        }

        .function-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.4);
        }

        .function-item h3 {
            margin: 0 0 0.5rem 0;
            color: inherit;
            font-size: 1.1rem;
            font-weight: 600;
            line-height: 1.4;
            display: flex;
            align-items: center;
        }

        .function-item h3::before {
            content: '⚡';
            margin-right: 0.5rem;
            font-size: 1.2rem;
        }

        .function-item p {
            margin: 0;
            color: #6b7280;
            font-size: 0.9rem;
            line-height: 1.5;
            opacity: 0.8;
        }

        .function-item.active p {
            color: rgba(255, 255, 255, 0.9);
        }

        .function-item::after {
            content: '→';
            position: absolute;
            right: 1.5rem;
            top: 50%;
            transform: translateY(-50%);
            color: #667eea;
            font-size: 1.2rem;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .function-item:hover::after,
        .function-item.active::after {
            opacity: 1;
            transform: translateY(-50%) translateX(4px);
        }

        .function-item.active::after {
            color: white;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 移动端优化 */
        @media (max-width: 767px) {
            .repair-container {
                padding: 1rem 0.75rem;
            }

            .repair-header {
                font-size: 2rem;
                margin-bottom: 2rem;
            }

            .function-list {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .function-item {
                padding: 1.25rem;
            }
        }

        /* 演示说明 */
        .demo-note {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 2rem;
            color: white;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="repair-container">
        <h1 class="repair-header">IT运维工具</h1>
        
        <div class="demo-note">
            <p>🎨 这是重新设计后的页面样式演示 - 现代化的渐变背景、毛玻璃效果、流畅的动画和响应式设计</p>
        </div>

        <!-- 功能选择列表 -->
        <div class="function-list">
            <div class="function-item">
                <h3>系统监控</h3>
                <p>点击选择此功能进行操作</p>
            </div>
            <div class="function-item">
                <h3>日志分析</h3>
                <p>点击选择此功能进行操作</p>
            </div>
            <div class="function-item active">
                <h3>性能优化</h3>
                <p>点击选择此功能进行操作</p>
            </div>
            <div class="function-item">
                <h3>安全检查</h3>
                <p>点击选择此功能进行操作</p>
            </div>
            <div class="function-item">
                <h3>备份恢复</h3>
                <p>点击选择此功能进行操作</p>
            </div>
            <div class="function-item">
                <h3>网络诊断</h3>
                <p>点击选择此功能进行操作</p>
            </div>
        </div>
    </div>

    <script>
        // 简单的交互演示
        document.querySelectorAll('.function-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.function-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
