<style>
    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
    }

    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    }

    .repair-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem 1rem;
        min-height: 100vh;
    }

    .repair-header {
        text-align: center;
        margin-bottom: 3rem;
        color: #ffffff;
        font-size: 2.5rem;
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        position: relative;
    }

    .repair-header::after {
        content: '';
        display: block;
        width: 80px;
        height: 4px;
        background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
        margin: 1rem auto 0;
        border-radius: 2px;
    }

    /* 功能列表样式 */
    .function-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .function-item {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: none;
        border-radius: 16px;
        padding: 1.5rem;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        text-align: left;
        position: relative;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .function-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .function-item:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        background: rgba(255, 255, 255, 1);
    }

    .function-item:hover::before {
        transform: scaleX(1);
    }

    .function-item.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 40px rgba(102, 126, 234, 0.4);
    }

    .function-item.active::before {
        background: rgba(255, 255, 255, 0.3);
        transform: scaleX(1);
    }

    .function-item h3 {
        margin: 0 0 0.5rem 0;
        color: inherit;
        font-size: 1.1rem;
        font-weight: 600;
        line-height: 1.4;
        display: flex;
        align-items: center;
    }

    .function-item h3::before {
        content: '';
        margin-right: 0.5rem;
        font-size: 1.2rem;
    }

    .function-item.active h3 {
        color: white;
    }

    .function-item p {
        margin: 0;
        color: #6b7280;
        font-size: 0.9rem;
        line-height: 1.5;
        opacity: 0.8;
    }

    .function-item.active p {
        color: rgba(255, 255, 255, 0.9);
    }

    .function-item::after {
        content: '→';
        position: absolute;
        right: 1.5rem;
        top: 50%;
        transform: translateY(-50%);
        color: #667eea;
        font-size: 1.2rem;
        opacity: 0;
        transition: all 0.3s ease;
    }

    .function-item:hover::after,
    .function-item.active::after {
        opacity: 1;
        transform: translateY(-50%) translateX(4px);
    }

    .function-item.active::after {
        color: white;
    }

    /* 操作区域样式 */
    .operation-area {
        display: none;
        grid-template-columns: 1fr;
        gap: 1.5rem;
        margin-top: 2rem;
        animation: fadeInUp 0.5s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .operation-area.active {
        display: grid;
    }

    @media (min-width: 768px) {
        .operation-area.active {
            grid-template-columns: 1.2fr 0.8fr;
            gap: 2rem;
        }
    }

    .repair-form-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .repair-form-container h3 {
        color: #1f2937;
        margin: 0 0 1rem 0;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #f1f5f9;
        font-size: 1.2rem;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .repair-form-container h3::before {
        content: '';
        margin-right: 0.5rem;
        font-size: 1.2rem;
    }

    .repair-form {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        padding: 1.5rem;
        border-radius: 12px;
        margin-top: 1rem;
        border: 1px solid #e2e8f0;
    }

    .repair-form p {
        margin: 0 0 0.5rem 0;
        color: #374151;
        font-size: 0.9rem;
        line-height: 1.5;
        font-weight: 500;
    }

    .repair-form input[type="text"],
    .repair-form textarea,
    .repair-form select {
        padding: 0.75rem 1rem;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        width: 100%;
        font-family: inherit;
        background: #ffffff;
        line-height: 1.5;
    }

    .repair-form input[type="text"]:focus,
    .repair-form textarea:focus,
    .repair-form select:focus {
        border-color: #667eea;
        outline: none;
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        background: #ffffff;
        transform: translateY(-1px);
    }

    .repair-form textarea {
        resize: vertical;
        min-height: 80px;
        font-family: inherit;
    }

    .repair-form select {
        cursor: pointer;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23667eea' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 1rem center;
        background-repeat: no-repeat;
        background-size: 1.2rem;
        padding-right: 3rem;
    }

    .repair-submit {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 600;
        transition: all 0.3s ease;
        align-self: flex-start;
        margin-top: 0.5rem;
        min-width: 120px;
        line-height: 1.5;
        position: relative;
        overflow: hidden;
    }

    .repair-submit::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .repair-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .repair-submit:hover::before {
        left: 100%;
    }

    .repair-submit:active {
        transform: translateY(0);
    }

    .repair-submit:disabled {
        background: #9ca3af;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .repair-submit:disabled::before {
        display: none;
    }

    .response-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        position: sticky;
        top: 1rem;
        height: fit-content;
    }

    .response-container h3 {
        color: #1f2937;
        margin: 0 0 1rem 0;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #f1f5f9;
        font-size: 1.2rem;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .response-container h3::before {
        content: '📊';
        margin-right: 0.5rem;
        font-size: 1.2rem;
    }

    .response-html {
        padding: 1rem;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-radius: 12px;
        min-height: 200px;
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid #e2e8f0;
        font-size: 0.9rem;
        line-height: 1.6;
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(4px);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }

    .loading-content {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        padding: 2.5rem;
        border-radius: 16px;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(102, 126, 234, 0.2);
        border-top: 4px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1.5rem;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .success-message {
        color: #059669;
        padding: 1rem 1.5rem;
        background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
        border-radius: 12px;
        margin: 1rem 0;
        border-left: 4px solid #10b981;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.1);
    }

    .error-message {
        color: #dc2626;
        padding: 1rem 1.5rem;
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        border-radius: 12px;
        margin: 1rem 0;
        border-left: 4px solid #ef4444;
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.1);
    }

    .back-button {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        color: white;
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .back-button::before {
        content: '←';
        font-size: 1.1rem;
    }

    .back-button:hover {
        background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(75, 85, 99, 0.3);
    }

    /* 移动端优化 */
    @media (max-width: 767px) {
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .repair-container {
            padding: 1rem 0.75rem;
        }

        .repair-header {
            font-size: 2rem;
            margin-bottom: 2rem;
        }

        .function-list {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .function-item {
            padding: 1.25rem;
        }

        .function-item h3 {
            font-size: 1rem;
        }

        .operation-area {
            gap: 1.5rem;
            margin-top: 1.5rem;
        }

        .repair-form-container,
        .response-container {
            padding: 1.25rem;
        }

        .response-container {
            position: static;
        }

        .repair-form {
            padding: 1.25rem;
        }

        .repair-submit {
            width: 100%;
            align-self: stretch;
            padding: 1rem 1.5rem;
        }

        .back-button {
            width: 100%;
            justify-content: center;
        }
    }

    /* 超小屏幕优化 */
    @media (max-width: 480px) {
        .repair-container {
            padding: 1rem 0.5rem;
        }

        .repair-header {
            font-size: 1.75rem;
            margin-bottom: 1.5rem;
        }

        .function-item {
            padding: 1rem;
        }

        .repair-form-container,
        .response-container {
            padding: 1rem;
        }

        .repair-form {
            padding: 1rem;
        }
    }

    /* 深色模式支持 */
    @media (prefers-color-scheme: dark) {
        .function-item {
            background: rgba(30, 41, 59, 0.95);
            color: #e2e8f0;
        }

        .function-item h3 {
            color: #f1f5f9;
        }

        .function-item p {
            color: #94a3b8;
        }

        .repair-form-container,
        .response-container {
            background: rgba(30, 41, 59, 0.95);
            color: #e2e8f0;
        }

        .repair-form-container h3,
        .response-container h3 {
            color: #f1f5f9;
        }

        .repair-form {
            background: rgba(15, 23, 42, 0.5);
        }

        .response-html {
            background: rgba(15, 23, 42, 0.5);
            color: #e2e8f0;
        }

        .loading-content {
            background: rgba(30, 41, 59, 0.95);
            color: #e2e8f0;
        }
    }

    /* 动画增强 */
    .function-item,
    .repair-form-container,
    .response-container {
        animation: slideInUp 0.6s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 滚动条美化 */
    .response-html::-webkit-scrollbar {
        width: 8px;
    }

    .response-html::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 4px;
    }

    .response-html::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 4px;
    }

    .response-html::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #5a67d8, #6b46c1);
    }
</style>

<div class="repair-container">
    <h1 class="repair-header">IT运维工具</h1>

    <!-- 功能选择列表 -->
    <div class="function-list" id="function-list">
        <{if $html}>
        <{foreach from=$html key=key item=value}>
        <div class="function-item" data-function="<{$key}>">
            <h3><{$value.desc}></h3>
            <p>点击选择此功能进行操作</p>
        </div>
        <{/foreach}>
        <{/if}>
    </div>

    <!-- 操作区域 -->
    <div class="operation-area" id="operation-area">
        <div class="repair-form-container">
            <button class="back-button" onclick="backToFunctionList()">返回功能列表</button>
            <h3 id="current-function-title">功能操作</h3>
            <div class="repair-form" id="repair-form" data-url="<{$process_url}>">
                <!-- 动态加载的表单内容 -->
            </div>
        </div>

        <div class="response-container">
            <h3>执行结果</h3>
            <div class="response-html" id="pair_right_response_html">
                <p style="color: #666; text-align: center; padding: 2rem;">请选择功能并执行操作，结果将在此处显示</p>
            </div>
        </div>
    </div>
</div>

<div class="loading-overlay" id="cover_float">
    <div class="loading-content">
        <div class="loading-spinner"></div>
        <div>处理中，请稍候...</div>
    </div>
</div>

<!-- 隐藏的表单模板 -->
<div style="display: none;">
    <{if $html}>
    <{foreach from=$html key=key item=value}>
    <div id="form-template-<{$key}>">
        <{$value.html}>
        <button class="repair-submit" data-sub-type="pair_submit_item">提交</button>
    </div>
    <{/foreach}>
    <{/if}>
</div>

<!-- 功能数据配置 -->
<script type="text/javascript">
    window.repairFunctionData = {
        <{if $html}>
        <{foreach from=$html key=key item=value name=func_loop}>
        '<{$key}>': {
            title: '<{$value.desc|escape:"javascript"}>',
            html: null
        }<{if !$smarty.foreach.func_loop.last}>,<{/if}>
        <{/foreach}>
        <{/if}>
    };
</script>

<script>
    // 全局变量
    let currentFunction = null;
    let functionData = window.repairFunctionData || {};

    // 初始化功能数据
    function initFunctionData() {
        // 获取表单HTML内容
        for (let key in functionData) {
            const templateEl = $('form-template-' + key);
            if (templateEl) {
                functionData[key].html = templateEl.get('html');
            }
        }
    }

    // 页面加载完成后初始化
    window.addEvent('domready', function() {
        initFunctionData();
        initFunctionSelection();
        initFormSubmission();
    });

    // 初始化功能选择
    function initFunctionSelection() {
        $$('.function-item').addEvent('click', function() {
            const functionKey = this.get('data-function');
            selectFunction(functionKey);
        });
    }

    // 选择功能
    function selectFunction(functionKey) {
        if (!functionData[functionKey]) return;

        currentFunction = functionKey;

        // 更新UI状态
        $$('.function-item').removeClass('active');
        $$('.function-item[data-function="' + functionKey + '"]').addClass('active');

        // 显示操作区域
        $('function-list').setStyle('display', 'none');
        $('operation-area').addClass('active');

        // 更新标题和表单内容
        $('current-function-title').set('text', functionData[functionKey].title);
        $('repair-form').set('html', functionData[functionKey].html);

        // 重新绑定提交事件
        initFormSubmission();

        // 清空结果区域
        responseEmpty();
    }

    // 返回功能列表
    function backToFunctionList() {
        $('function-list').setStyle('display', 'grid');
        $('operation-area').removeClass('active');
        $$('.function-item').removeClass('active');
        currentFunction = null;
        responseEmpty();
    }

    // 初始化表单提交
    function initFormSubmission() {
        // 移除旧的事件监听器，避免重复绑定
        $$('.repair-submit').removeEvents('click');

        $$('.repair-submit').addEvent('click', function() {
            if (!currentFunction) return;

            const button = this;
            const form = button.getParent('.repair-form');
            const subUrl = form.get('data-url');
            const subDataString = form.toQueryString();
            const taskName = functionData[currentFunction].title;

            // 禁用提交按钮
            button.set('disabled', true);
            button.set('text', '处理中...');

            responseEmpty();

            const request = new Request({
                url: subUrl,
                method: 'post',
                data: subDataString,
                onRequest: function() {
                    $("cover_float").setStyle('display', 'flex');
                },
                onSuccess: function(responseText) {
                    $("cover_float").setStyle('display', 'none');
                    responseHandle(responseText, taskName);

                    // 恢复提交按钮
                    button.set('disabled', false);
                    button.set('text', '提交');
                },
                onFailure: function() {
                    $("cover_float").setStyle('display', 'none');
                    responseHandle('{"code":500,"msg":"网络请求失败，请检查网络连接后重试"}', taskName);

                    // 恢复提交按钮
                    button.set('disabled', false);
                    button.set('text', '提交');
                }
            });

            request.send();
        });
    }

    // 处理响应结果
    function responseHandle(responseText, taskName) {
        const responseAreaHtml = $("pair_right_response_html");
        let json;

        try {
            json = JSON.decode(responseText);
        } catch (e) {
            json = {code: 500, msg: '响应数据格式错误'};
        }

        let html = '';
        const timestamp = new Date().toLocaleString();

        html += `<div style="border-bottom: 1px solid #e0e0e0; padding-bottom: 0.5rem; margin-bottom: 1rem;">`;
        html += `<h4 style="margin: 0; color: #2c3e50;">${taskName}</h4>`;
        html += `<small style="color: #666;">执行时间: ${timestamp}</small>`;
        html += `</div>`;

        if(json.code == 200) {
            html += `<div class="success-message">`;
            html += `<strong>✓ 执行成功</strong><br>`;
            html += `${json.msg}`;
            html += `</div>`;
        } else if(json.code == 500) {
            html += `<div class="error-message">`;
            html += `<strong>✗ 执行失败</strong><br>`;
            html += `${json.msg}`;
            html += `</div>`;
        } else {
            html += `<div class="error-message">`;
            html += `<strong>? 未知状态</strong><br>`;
            html += `响应代码: ${json.code}<br>`;
            html += `响应消息: ${json.msg || '无消息'}`;
            html += `</div>`;
        }

        responseAreaHtml.set('html', html);

        // 滚动到结果区域（移动端优化）
        if (window.innerWidth <= 767) {
            responseAreaHtml.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }

    // 清空响应区域
    function responseEmpty() {
        $("pair_right_response_html").set('html', '<p style="color: #666; text-align: center; padding: 2rem;">请执行操作，结果将在此处显示</p>');
    }

    // 键盘快捷键支持
    document.addEvent('keydown', function(e) {
        // ESC键返回功能列表
        if (e.key === 'Escape' && currentFunction) {
            backToFunctionList();
        }
    });

    // 移动端触摸优化
    if ('ontouchstart' in window) {
        $$('.function-item').addEvent('touchstart', function() {
            this.addClass('active');
        });

        $$('.function-item').addEvent('touchend', function() {
            // 延迟移除active状态，避免点击效果过快消失
            setTimeout(() => {
                if (this.get('data-function') !== currentFunction) {
                    this.removeClass('active');
                }
            }, 150);
        });
    }
</script>
